import re

str_data = """FOR I IN (

            SELECT

                (

                    CASE WHEN (

                        SELECT

                            unnest(xpath('E', XT.COLUMN_VALUE)))::text = '' THEN

                        NULL

                    ELSE

                        (

                            SELECT

                                unnest(xpath('E', XT.COLUMN_VALUE)))::text

                    END) AS SEROTESTIDS

            FROM

                TABLE (XMLSEQUENCE (extract(xml(REPLACE(('<X><E>' || (

                                        SELECT

                                            BCNF.CONFIGVALUE

                                        FROM BB.BBCONFIG BCNF

                                    WHERE

                                        UPPER(BCNF.CONFIG_KEY) = 'ISSEROPOSITIVETESTS') || '</E></X>'), ',', '</E><E>')), '/X/E'))) XT)

            LOOP

                EXIT

    WHEN I.SEROTESTIDS IS NULL;"""
def xml_sequence(data, schema):
    select_pattern = re.findall(r'\bSELECT\b.*?;', data, flags=re.IGNORECASE | re.DOTALL)
    for select_query in select_pattern:

        check = re.search(r'\s*TABLE\s*\(\s*XMLSEQUENCE\s*\(', select_query, flags=re.DOTALL | re.I)
        if check:
            from_table = re.findall(r'\s*TABLE\s*\(.*?;', select_query, flags=re.DOTALL | re.I)
            for from_tab in from_table:
                from_manp = re.sub(r'\bTABLE\s*\(\s*XMLSEQUENCE\s*\(', '(WITH ctc AS ', from_tab, flags=re.DOTALL | re.I)
                from_manp = re.sub(r'(?:\:\s*\:\s*text|:\:\s*\:\s*numeric)', '', from_manp, flags=re.DOTALL | re.I)
                xml_stmt = re.findall(r'\(\s*SELECT\s*UNNEST\s*\(\s*XPATH\s*\(.*?\)\s*\)\s*\)', from_manp, flags=re.DOTALL | re.I)
                for xml in xml_stmt:
                    xml1 = re.sub(r'\)\s*\)\s*\)', ')) AS value ) SELECT value FROM ctc )', xml, flags=re.DOTALL | re.I)
                    from_manp = from_manp.replace(xml, xml1)
                from_manp = re.sub(r'\bctc\s*\)\s*\)', 'ctc )', from_manp, flags=re.DOTALL | re.I)
                data = data.replace(from_tab, from_manp)

    return data


print(x)